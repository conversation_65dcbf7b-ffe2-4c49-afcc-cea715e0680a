using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using AutoMapper;
using EAMS.Domain.Aggregates;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger)
    {
        _accommodationService = accommodationService;
        _logger = logger;
    }

    /// <summary>
    /// Get all accommodations
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations()
    {
        var accommodations = await _accommodationService.GetAll();
        return Ok(accommodations);
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(long id)
    {
        var accommodation = await _accommodationService.GetById(id);

        if (accommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", id);
        }

        return Ok(accommodation);
    }

    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(AccommodationDto accommodationDto)
    {
        var createdAccommodation = await _accommodationService.Create(accommodationDto);
        return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, createdAccommodation);
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(long id, AccommodationDto accommodationDto)
    {
        // Ensure the ID in the URL matches the DTO
        accommodationDto.Id = id;

        var updatedAccommodation = await _accommodationService.Update(accommodationDto);
        return Ok(updatedAccommodation);
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(long id)
    {
        var result = await _accommodationService.Delete(id);
        return Ok(result);
    }
}
