using EAMS.API.Configurations;
using EAMS.API.Middleware;
using EAMS.API.Filters;
using EAMS.Infrastructure.Data;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web;
using System.Reflection;
using EAMS.API.Extensions;
using Azure.Identity;
using Microsoft.Graph;
using EAMS.Application.Mappings;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Configure the application to use the MS Identiy Platform
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"))
    .EnableTokenAcquisitionToCallDownstreamApi()
    .AddMicrosoftGraph(options =>
                           builder.Configuration.GetSection("GraphApi").Bind(options))
    .AddInMemoryTokenCaches();


builder.Services.AddKeyedScoped<GraphServiceClient>("applicationGraph", (sp, key) =>
{
    var config = builder.Configuration.GetSection("AzureAd");
    var scopes = new string[] { "https://graph.microsoft.com/.default" };
    var tenantId = config["TenantId"];
    var clientId = config["ClientId"];
    var clientSecret = config["ClientSecret"];

    // using Azure.Identity;
    var options = new ClientSecretCredentialOptions
    {
        AuthorityHost = AzureAuthorityHosts.AzurePublicCloud,
    };

    // https://learn.microsoft.com/dotnet/api/azure.identity.clientsecretcredential
    var clientSecretCredential = new ClientSecretCredential(
        tenantId, clientId, clientSecret, options);

    return new GraphServiceClient(clientSecretCredential, scopes);
});

builder.Services.AddScoped<ICorrelationIdGenerator, CorrelationIdGenerator>();
builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly(), typeof(AccommodationProfile).Assembly);

builder.Services.AddControllers(options =>
{
    // Add global filters
    options.Filters.Add<ModelStateValidationFilter>();
    options.Filters.Add<IdMismatchValidationFilter>();
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerDocumentation(builder.Configuration);

builder.Services.AddDbContext<EamsDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlServerOptions => sqlServerOptions.UseNetTopologySuite()));

// Register services
builder.Services.AddRepositoryServices();
builder.Services.AddApplicationServices();
builder.Services.AddDomainServices();

builder.Logging.AddApplicationInsights(
        configureTelemetryConfiguration: (config) =>
            config.ConnectionString = builder.Configuration.GetConnectionString("APPLICATIONINSIGHTS_CONNECTION_STRING"),
            configureApplicationInsightsLoggerOptions: (options) => { }
    );
builder.Services.AddApplicationInsightsTelemetry();

builder.Logging.AddFilter<ApplicationInsightsLoggerProvider>("eams-bff", LogLevel.Trace);
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "EAMS API");
        c.OAuthClientId(builder.Configuration["SwaggerOAuth:ClientId"]!);
        c.OAuthUsePkce();
        c.OAuthScopeSeparator(" ");
    });
}

app.UseHttpsRedirection();
app.UseMiddleware<GlobalExceptionHandlingMiddleware>();

app.UseAuthorization();
app.UseCors(x => x
                .SetIsOriginAllowed(origin => true)
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials());

app.MapControllers();
app.UseMiddleware<CorrelationIdMiddleware>();
app.UseMiddleware<TelemetryLoggingMiddleware>();

app.Run();
