using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Domain.Aggregates;
using EAMS.Domain.ValueObjects;

namespace EAMS.Application.Mappings;

public class AccommodationProfile : Profile
{
    public AccommodationProfile()
    {
        // Accommodation to AccommodationDto mapping
        CreateMap<Accommodation, AccommodationDto>()
            .ForMember(dest => dest.Latitude, opt => opt.MapFrom(src =>
                src.Location.HasValue ? src.Location.Value.Latitude : (double?)null))
            .ForMember(dest => dest.Longitude, opt => opt.MapFrom(src =>
                src.Location.HasValue ? src.Location.Value.Longitude : (double?)null))
            .ForMember(dest => dest.DurationIds, opt => opt.MapFrom(src =>
                src.Duration.Select(d => d.Id).ToList()))
            .ForMember(dest => dest.AmenityIds, opt => opt.MapFrom(src =>
                src.AmenityOptions.Select(ao => ao.Id).ToList()));

        // AccommodationDto to Accommodation mapping
        CreateMap<AccommodationDto, Accommodation>()
            .ForMember(dest => dest.Location, opt => opt.MapFrom(src =>
                src.Latitude.HasValue && src.Longitude.HasValue
                    ? GeoPoint.Create(src.Latitude.Value, src.Longitude.Value)
                    : (GeoPoint?)null))
            .ForMember(dest => dest.Duration, opt => opt.Ignore())
            .ForMember(dest => dest.AmenityOptions, opt => opt.Ignore());

        // Amenity mapping
        CreateMap<Amenity, AmenityDto>().ReverseMap();

        // AmenityOptions mapping
        CreateMap<AmenityOptions, AmenityOptionsDto>().ReverseMap();
        CreateMap<AccommodationType, EnumDto>().ReverseMap();
        CreateMap<AmenityType, EnumDto>().ReverseMap();
        CreateMap<Density, EnumDto>().ReverseMap();
        CreateMap<Duration, EnumDto>().ReverseMap();
        CreateMap<Region, EnumDto>().ReverseMap();
    }
}
