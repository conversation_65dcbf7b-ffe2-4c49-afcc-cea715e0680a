using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq;
using System.Linq.Expressions;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;
    private readonly IMapper _mapper;
    private readonly IValidationService _validationService;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IAmenityOptionsRepository _amenityOptionsRepository;

    public AccommodationService(
        IAccommodationRepository accommodationRepository,
        IMapper mapper,
        IValidationService validationService,
        IRepository<Duration, int> durationRepository,
        IAmenityOptionsRepository amenityOptionsRepository)
    {
        _accommodationRepository = accommodationRepository;
        _mapper = mapper;
        _validationService = validationService;
        _durationRepository = durationRepository;
        _amenityOptionsRepository = amenityOptionsRepository;
    }

    public async Task<IEnumerable<AccommodationDto>> GetAll()
    {
        var accommodations = await _accommodationRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
    }

    public async Task<AccommodationDto?> GetById(long id)
    {
        var accommodation = await _accommodationRepository.GetByIdAsync(id);
        return accommodation != null ? _mapper.Map<AccommodationDto>(accommodation) : null;
    }

    public async Task<AccommodationDto> Create(AccommodationDto accommodationDto)
    {
        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core can populate the join table
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.Id))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();

        // Set timestamps for new entity using validation service
        // _validationService.SetTimestampsForCreate(accommodation);

        await _accommodationRepository.AddAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<AccommodationDto> Update(AccommodationDto accommodationDto)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodationDto.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodationDto.Id);
        }

        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core updates the join table correctly
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.Id))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // Set timestamps for updated entity using validation service
        // _validationService.SetTimestampsForUpdate(accommodation, existingAccommodation);

        await _accommodationRepository.UpdateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<bool> Delete(long id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        await _accommodationRepository.DeleteAsync(id);
        return true;
    }
}
