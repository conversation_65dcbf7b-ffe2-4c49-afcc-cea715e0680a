using EAMS.Domain.Aggregates;
using EAMS.Domain.Repositories;
using EAMS.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace EAMS.Infrastructure.Repositories
{
    public class AccommodationRepository : Repository<Accommodation, Int64>, IAccommodationRepository
    {
        public AccommodationRepository(EamsDbContext context) : base(context)
        {
        }

        public override Task<IEnumerable<Accommodation>> GetAllAsync(
            Expression<Func<Accommodation, bool>>? where = null,
            params Expression<Func<Accommodation, object>>[] includes)
        {
            var defaultIncludes = new List<Expression<Func<Accommodation, object>>>
            {
                accommodation => accommodation.Duration,
                accommodation => accommodation.AmenityOptions
            };

            if (includes?.Length > 0)
            {
                defaultIncludes.AddRange(includes);
            }

            return base.GetAllAsync(where, defaultIncludes.ToArray());
        }

        public override Task<Accommodation?> GetByIdAsync(
            long id,
            params Expression<Func<Accommodation, object>>[] includes)
        {
            var defaultIncludes = new List<Expression<Func<Accommodation, object>>>
            {
                accommodation => accommodation.Duration,
                accommodation => accommodation.AmenityOptions
            };

            if (includes?.Length > 0)
            {
                defaultIncludes.AddRange(includes);
            }

            return base.GetByIdAsync(id, defaultIncludes.ToArray());
        }
    }
}
